<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>社交App原型设计</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: #f5f5f5;
            color: #333;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }
        
        .screen-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(320px, 1fr));
            gap: 30px;
            margin-bottom: 40px;
        }
        
        .phone-frame {
            width: 320px;
            height: 640px;
            background: #000;
            border-radius: 25px;
            padding: 8px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.3);
            margin: 0 auto;
        }
        
        .screen {
            width: 100%;
            height: 100%;
            background: #fff;
            border-radius: 18px;
            overflow: hidden;
            position: relative;
        }
        
        .status-bar {
            height: 24px;
            background: linear-gradient(135deg, #ff6b9d, #c44569);
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 0 15px;
            color: white;
            font-size: 12px;
        }
        
        .header {
            height: 60px;
            background: linear-gradient(135deg, #ff6b9d, #c44569);
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 0 20px;
            color: white;
        }
        
        .header h1 {
            font-size: 18px;
            font-weight: 600;
        }
        
        .content {
            height: calc(100% - 144px);
            overflow-y: auto;
            padding: 0;
        }
        
        .bottom-nav {
            height: 60px;
            background: white;
            border-top: 1px solid #eee;
            display: flex;
            justify-content: space-around;
            align-items: center;
        }
        
        .nav-item {
            display: flex;
            flex-direction: column;
            align-items: center;
            color: #999;
            font-size: 10px;
            position: relative;
        }
        
        .nav-item.active {
            color: #ff6b9d;
        }
        
        .nav-item i {
            font-size: 20px;
            margin-bottom: 4px;
        }
        
        .badge {
            position: absolute;
            top: -5px;
            right: -8px;
            background: #ff4757;
            color: white;
            border-radius: 10px;
            padding: 2px 6px;
            font-size: 10px;
            min-width: 16px;
            text-align: center;
        }
        
        .post {
            background: white;
            margin-bottom: 10px;
            padding: 15px;
        }
        
        .post-header {
            display: flex;
            align-items: center;
            margin-bottom: 10px;
        }
        
        .avatar {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            background: linear-gradient(45deg, #ff6b9d, #ffa726);
            margin-right: 10px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-weight: bold;
        }
        
        .user-info h3 {
            font-size: 14px;
            margin-bottom: 2px;
        }
        
        .user-info .distance {
            font-size: 12px;
            color: #999;
        }
        
        .post-content {
            margin-bottom: 10px;
            line-height: 1.4;
        }
        
        .post-image {
            width: 100%;
            height: 200px;
            background: linear-gradient(45deg, #ffeaa7, #fab1a0);
            border-radius: 8px;
            margin-bottom: 10px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 14px;
        }
        
        .post-actions {
            display: flex;
            justify-content: space-around;
            padding-top: 10px;
            border-top: 1px solid #eee;
        }
        
        .action-btn {
            display: flex;
            align-items: center;
            color: #999;
            font-size: 12px;
        }
        
        .action-btn i {
            margin-right: 5px;
        }
        
        .screen-title {
            text-align: center;
            margin-bottom: 20px;
            font-size: 16px;
            font-weight: 600;
            color: #333;
        }
        
        .nearby-user {
            background: white;
            margin-bottom: 10px;
            padding: 15px;
            display: flex;
            align-items: center;
        }
        
        .nearby-user .avatar {
            width: 50px;
            height: 50px;
            margin-right: 15px;
        }
        
        .nearby-info {
            flex: 1;
        }
        
        .nearby-info h3 {
            font-size: 16px;
            margin-bottom: 5px;
        }
        
        .nearby-info .age-distance {
            font-size: 12px;
            color: #999;
        }
        
        .chat-item {
            background: white;
            padding: 15px;
            border-bottom: 1px solid #eee;
            display: flex;
            align-items: center;
        }
        
        .chat-item .avatar {
            margin-right: 15px;
        }
        
        .chat-info {
            flex: 1;
        }
        
        .chat-info h3 {
            font-size: 14px;
            margin-bottom: 5px;
        }
        
        .chat-info .last-message {
            font-size: 12px;
            color: #999;
        }
        
        .chat-time {
            font-size: 12px;
            color: #999;
        }
        
        .profile-header {
            background: linear-gradient(135deg, #ff6b9d, #c44569);
            padding: 30px 20px;
            text-align: center;
            color: white;
        }
        
        .profile-avatar {
            width: 80px;
            height: 80px;
            border-radius: 50%;
            background: rgba(255,255,255,0.2);
            margin: 0 auto 15px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 24px;
        }
        
        .profile-name {
            font-size: 20px;
            margin-bottom: 5px;
        }
        
        .profile-bio {
            font-size: 14px;
            opacity: 0.9;
        }
        
        .menu-item {
            background: white;
            padding: 15px 20px;
            border-bottom: 1px solid #eee;
            display: flex;
            align-items: center;
            justify-content: space-between;
        }
        
        .menu-item-left {
            display: flex;
            align-items: center;
        }
        
        .menu-item-left i {
            width: 20px;
            margin-right: 15px;
            color: #ff6b9d;
        }
        
        .fab {
            position: absolute;
            bottom: 80px;
            right: 20px;
            width: 56px;
            height: 56px;
            background: linear-gradient(135deg, #ff6b9d, #c44569);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 24px;
            box-shadow: 0 4px 12px rgba(255,107,157,0.4);
        }

        .live-category {
            display: flex;
            padding: 15px 20px 10px;
            gap: 15px;
            overflow-x: auto;
            background: white;
        }

        .category-item {
            flex-shrink: 0;
            padding: 8px 16px;
            background: #f8f9fa;
            border-radius: 20px;
            font-size: 14px;
            color: #666;
            border: 1px solid #eee;
        }

        .category-item.active {
            background: linear-gradient(135deg, #ff6b9d, #c44569);
            color: white;
            border-color: transparent;
        }

        .live-grid {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 10px;
            padding: 10px;
        }

        .live-card {
            background: white;
            border-radius: 12px;
            overflow: hidden;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            position: relative;
        }

        .live-thumbnail {
            width: 100%;
            height: 120px;
            background: linear-gradient(45deg, #667eea, #764ba2);
            position: relative;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 12px;
        }

        .live-status {
            position: absolute;
            top: 8px;
            left: 8px;
            background: #ff4757;
            color: white;
            padding: 2px 8px;
            border-radius: 10px;
            font-size: 10px;
            font-weight: bold;
        }

        .live-viewers {
            position: absolute;
            top: 8px;
            right: 8px;
            background: rgba(0,0,0,0.5);
            color: white;
            padding: 2px 6px;
            border-radius: 10px;
            font-size: 10px;
        }

        .live-info {
            padding: 10px;
        }

        .live-title {
            font-size: 12px;
            font-weight: 500;
            margin-bottom: 5px;
            color: #333;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
        }

        .live-host {
            display: flex;
            align-items: center;
            font-size: 11px;
            color: #666;
        }

        .live-host .avatar {
            width: 20px;
            height: 20px;
            margin-right: 5px;
            font-size: 10px;
        }

        .live-tags {
            display: flex;
            gap: 5px;
            margin-top: 5px;
        }

        .live-tag {
            background: #f0f0f0;
            color: #666;
            padding: 2px 6px;
            border-radius: 8px;
            font-size: 9px;
        }

        .search-bar {
            margin: 15px 20px;
            position: relative;
        }

        .search-input {
            width: 100%;
            padding: 10px 40px 10px 15px;
            border: 1px solid #eee;
            border-radius: 20px;
            background: #f8f9fa;
            font-size: 14px;
        }

        .search-icon {
            position: absolute;
            right: 15px;
            top: 50%;
            transform: translateY(-50%);
            color: #999;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1 style="text-align: center; margin-bottom: 40px; color: #333;">社交App原型设计</h1>
        
        <div class="screen-grid">
            <!-- 首页 - 动态流 -->
            <div>
                <div class="screen-title">首页 - 动态流</div>
                <div class="phone-frame">
                    <div class="screen">
                        <div class="status-bar">
                            <span>9:41</span>
                            <span><i class="fas fa-signal"></i> <i class="fas fa-wifi"></i> <i class="fas fa-battery-three-quarters"></i></span>
                        </div>
                        <div class="header">
                            <h1>动态</h1>
                            <i class="fas fa-paper-plane"></i>
                        </div>
                        <div class="content">
                            <div class="post">
                                <div class="post-header">
                                    <div class="avatar">小</div>
                                    <div class="user-info">
                                        <h3>坚强小猫咪</h3>
                                        <div class="distance">最近在线 · 146.73km</div>
                                    </div>
                                </div>
                                <div class="post-content">新人上路，希望附近的小伙伴多多关照</div>
                                <div class="post-image">📸 用户照片</div>
                                <div class="post-actions">
                                    <div class="action-btn"><i class="fas fa-thumbs-up"></i> 点赞</div>
                                    <div class="action-btn"><i class="fas fa-comment"></i> 评论</div>
                                    <div class="action-btn"><i class="fas fa-phone"></i> 打招呼</div>
                                </div>
                            </div>
                            <div class="post">
                                <div class="post-header">
                                    <div class="avatar">外</div>
                                    <div class="user-info">
                                        <h3>外向路人</h3>
                                        <div class="distance">最近在线 · 141.63km</div>
                                    </div>
                                </div>
                                <div class="post-content">新人来啦！周围有没有小伙伴可以关照一下</div>
                                <div class="post-image">📸 用户照片</div>
                                <div class="post-actions">
                                    <div class="action-btn"><i class="fas fa-thumbs-up"></i> 点赞</div>
                                    <div class="action-btn"><i class="fas fa-comment"></i> 评论</div>
                                    <div class="action-btn"><i class="fas fa-phone"></i> 打招呼</div>
                                </div>
                            </div>
                        </div>
                        <div class="bottom-nav">
                            <div class="nav-item active">
                                <i class="fas fa-home"></i>
                                <span>首页</span>
                            </div>
                            <div class="nav-item">
                                <i class="fas fa-video"></i>
                                <span>直播</span>
                                <div class="badge">19</div>
                            </div>
                            <div class="nav-item">
                                <i class="fas fa-comments"></i>
                                <span>消息</span>
                                <div class="badge">4</div>
                            </div>
                            <div class="nav-item">
                                <i class="fas fa-heart"></i>
                                <span>小宇宙</span>
                                <div class="badge">99+</div>
                            </div>
                            <div class="nav-item">
                                <i class="fas fa-user"></i>
                                <span>我的</span>
                                <div class="badge">1</div>
                            </div>
                        </div>
                        <div class="fab">
                            <i class="fas fa-plus"></i>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 附近的人 -->
            <div>
                <div class="screen-title">附近的人</div>
                <div class="phone-frame">
                    <div class="screen">
                        <div class="status-bar">
                            <span>9:41</span>
                            <span><i class="fas fa-signal"></i> <i class="fas fa-wifi"></i> <i class="fas fa-battery-three-quarters"></i></span>
                        </div>
                        <div class="header">
                            <h1>附近</h1>
                            <i class="fas fa-filter"></i>
                        </div>
                        <div class="content">
                            <div class="nearby-user">
                                <div class="avatar">美</div>
                                <div class="nearby-info">
                                    <h3>美丽女孩</h3>
                                    <div class="age-distance">22岁 · 距离500米</div>
                                </div>
                                <i class="fas fa-heart" style="color: #ff6b9d;"></i>
                            </div>
                            <div class="nearby-user">
                                <div class="avatar">阳</div>
                                <div class="nearby-info">
                                    <h3>阳光男孩</h3>
                                    <div class="age-distance">25岁 · 距离1.2km</div>
                                </div>
                                <i class="fas fa-heart" style="color: #ddd;"></i>
                            </div>
                            <div class="nearby-user">
                                <div class="avatar">温</div>
                                <div class="nearby-info">
                                    <h3>温柔小姐姐</h3>
                                    <div class="age-distance">24岁 · 距离2.1km</div>
                                </div>
                                <i class="fas fa-heart" style="color: #ddd;"></i>
                            </div>
                            <div class="nearby-user">
                                <div class="avatar">酷</div>
                                <div class="nearby-info">
                                    <h3>酷酷的我</h3>
                                    <div class="age-distance">26岁 · 距离3.5km</div>
                                </div>
                                <i class="fas fa-heart" style="color: #ddd;"></i>
                            </div>
                        </div>
                        <div class="bottom-nav">
                            <div class="nav-item">
                                <i class="fas fa-home"></i>
                                <span>首页</span>
                            </div>
                            <div class="nav-item">
                                <i class="fas fa-video"></i>
                                <span>直播</span>
                            </div>
                            <div class="nav-item">
                                <i class="fas fa-comments"></i>
                                <span>消息</span>
                            </div>
                            <div class="nav-item">
                                <i class="fas fa-heart"></i>
                                <span>小宇宙</span>
                            </div>
                            <div class="nav-item">
                                <i class="fas fa-user"></i>
                                <span>我的</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 直播广场 -->
            <div>
                <div class="screen-title">直播广场</div>
                <div class="phone-frame">
                    <div class="screen">
                        <div class="status-bar">
                            <span>9:41</span>
                            <span><i class="fas fa-signal"></i> <i class="fas fa-wifi"></i> <i class="fas fa-battery-three-quarters"></i></span>
                        </div>
                        <div class="header">
                            <h1>直播</h1>
                            <div style="display: flex; gap: 15px;">
                                <i class="fas fa-search"></i>
                                <i class="fas fa-video"></i>
                            </div>
                        </div>
                        <div class="content">
                            <!-- 搜索栏 -->
                            <div class="search-bar">
                                <input type="text" class="search-input" placeholder="搜索直播间或主播">
                                <i class="fas fa-search search-icon"></i>
                            </div>

                            <!-- 分类标签 -->
                            <div class="live-category">
                                <div class="category-item active">推荐</div>
                                <div class="category-item">唱歌</div>
                                <div class="category-item">聊天</div>
                                <div class="category-item">游戏</div>
                                <div class="category-item">才艺</div>
                                <div class="category-item">户外</div>
                            </div>

                            <!-- 直播网格 -->
                            <div class="live-grid">
                                <div class="live-card">
                                    <div class="live-thumbnail" style="background: linear-gradient(45deg, #ff6b9d, #c44569);">
                                        <span>🎤 唱歌直播</span>
                                        <div class="live-status">LIVE</div>
                                        <div class="live-viewers"><i class="fas fa-eye"></i> 1.2k</div>
                                    </div>
                                    <div class="live-info">
                                        <div class="live-title">甜美女声，深情演唱经典老歌</div>
                                        <div class="live-host">
                                            <div class="avatar">甜</div>
                                            <span>甜美歌声</span>
                                        </div>
                                        <div class="live-tags">
                                            <span class="live-tag">唱歌</span>
                                            <span class="live-tag">治愈</span>
                                        </div>
                                    </div>
                                </div>

                                <div class="live-card">
                                    <div class="live-thumbnail" style="background: linear-gradient(45deg, #667eea, #764ba2);">
                                        <span>🎮 游戏直播</span>
                                        <div class="live-status">LIVE</div>
                                        <div class="live-viewers"><i class="fas fa-eye"></i> 856</div>
                                    </div>
                                    <div class="live-info">
                                        <div class="live-title">王者荣耀上分局，带你飞</div>
                                        <div class="live-host">
                                            <div class="avatar">游</div>
                                            <span>游戏大神</span>
                                        </div>
                                        <div class="live-tags">
                                            <span class="live-tag">游戏</span>
                                            <span class="live-tag">王者</span>
                                        </div>
                                    </div>
                                </div>

                                <div class="live-card">
                                    <div class="live-thumbnail" style="background: linear-gradient(45deg, #ffeaa7, #fab1a0);">
                                        <span>💃 才艺表演</span>
                                        <div class="live-status">LIVE</div>
                                        <div class="live-viewers"><i class="fas fa-eye"></i> 2.1k</div>
                                    </div>
                                    <div class="live-info">
                                        <div class="live-title">舞蹈教学，一起学习热门舞蹈</div>
                                        <div class="live-host">
                                            <div class="avatar">舞</div>
                                            <span>舞蹈老师</span>
                                        </div>
                                        <div class="live-tags">
                                            <span class="live-tag">舞蹈</span>
                                            <span class="live-tag">教学</span>
                                        </div>
                                    </div>
                                </div>

                                <div class="live-card">
                                    <div class="live-thumbnail" style="background: linear-gradient(45deg, #a29bfe, #6c5ce7);">
                                        <span>💬 聊天互动</span>
                                        <div class="live-status">LIVE</div>
                                        <div class="live-viewers"><i class="fas fa-eye"></i> 445</div>
                                    </div>
                                    <div class="live-info">
                                        <div class="live-title">深夜电台，陪你聊天到天亮</div>
                                        <div class="live-host">
                                            <div class="avatar">夜</div>
                                            <span>夜猫主播</span>
                                        </div>
                                        <div class="live-tags">
                                            <span class="live-tag">聊天</span>
                                            <span class="live-tag">电台</span>
                                        </div>
                                    </div>
                                </div>

                                <div class="live-card">
                                    <div class="live-thumbnail" style="background: linear-gradient(45deg, #fd79a8, #e84393);">
                                        <span>🎨 绘画创作</span>
                                        <div class="live-status">LIVE</div>
                                        <div class="live-viewers"><i class="fas fa-eye"></i> 678</div>
                                    </div>
                                    <div class="live-info">
                                        <div class="live-title">手绘插画教程，零基础也能学</div>
                                        <div class="live-host">
                                            <div class="avatar">画</div>
                                            <span>画画达人</span>
                                        </div>
                                        <div class="live-tags">
                                            <span class="live-tag">绘画</span>
                                            <span class="live-tag">教程</span>
                                        </div>
                                    </div>
                                </div>

                                <div class="live-card">
                                    <div class="live-thumbnail" style="background: linear-gradient(45deg, #00b894, #00cec9);">
                                        <span>🌍 户外探险</span>
                                        <div class="live-status">LIVE</div>
                                        <div class="live-viewers"><i class="fas fa-eye"></i> 1.5k</div>
                                    </div>
                                    <div class="live-info">
                                        <div class="live-title">山间徒步，带你看最美风景</div>
                                        <div class="live-host">
                                            <div class="avatar">户</div>
                                            <span>户外达人</span>
                                        </div>
                                        <div class="live-tags">
                                            <span class="live-tag">户外</span>
                                            <span class="live-tag">旅行</span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="bottom-nav">
                            <div class="nav-item">
                                <i class="fas fa-home"></i>
                                <span>首页</span>
                            </div>
                            <div class="nav-item active">
                                <i class="fas fa-video"></i>
                                <span>直播</span>
                            </div>
                            <div class="nav-item">
                                <i class="fas fa-comments"></i>
                                <span>消息</span>
                            </div>
                            <div class="nav-item">
                                <i class="fas fa-heart"></i>
                                <span>小宇宙</span>
                            </div>
                            <div class="nav-item">
                                <i class="fas fa-user"></i>
                                <span>我的</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 直播间界面 -->
            <div>
                <div class="screen-title">直播间界面</div>
                <div class="phone-frame">
                    <div class="screen">
                        <div class="status-bar">
                            <span>9:41</span>
                            <span><i class="fas fa-signal"></i> <i class="fas fa-wifi"></i> <i class="fas fa-battery-three-quarters"></i></span>
                        </div>
                        <!-- 直播视频区域 -->
                        <div style="height: 300px; background: linear-gradient(45deg, #ff6b9d, #c44569); position: relative; display: flex; align-items: center; justify-content: center; color: white;">
                            <div style="text-align: center;">
                                <div style="font-size: 48px; margin-bottom: 10px;">🎤</div>
                                <div style="font-size: 16px;">甜美歌声正在直播</div>
                            </div>

                            <!-- 顶部信息栏 -->
                            <div style="position: absolute; top: 15px; left: 15px; right: 15px; display: flex; justify-content: space-between; align-items: center;">
                                <div style="display: flex; align-items: center; gap: 10px;">
                                    <i class="fas fa-arrow-left" style="color: white; font-size: 18px;"></i>
                                    <div style="display: flex; align-items: center; background: rgba(0,0,0,0.3); padding: 5px 10px; border-radius: 15px;">
                                        <div class="avatar" style="width: 25px; height: 25px; margin-right: 8px; font-size: 12px;">甜</div>
                                        <span style="font-size: 12px;">甜美歌声</span>
                                    </div>
                                </div>
                                <div style="display: flex; gap: 10px;">
                                    <div style="background: rgba(0,0,0,0.3); padding: 5px 10px; border-radius: 15px; font-size: 12px;">
                                        <i class="fas fa-eye"></i> 1.2k
                                    </div>
                                    <i class="fas fa-share-alt" style="color: white; font-size: 16px;"></i>
                                </div>
                            </div>

                            <!-- 右侧功能按钮 -->
                            <div style="position: absolute; right: 15px; top: 50%; transform: translateY(-50%); display: flex; flex-direction: column; gap: 20px;">
                                <div style="text-align: center;">
                                    <div style="width: 45px; height: 45px; background: rgba(255,255,255,0.2); border-radius: 50%; display: flex; align-items: center; justify-content: center; margin-bottom: 5px;">
                                        <i class="fas fa-heart" style="font-size: 20px;"></i>
                                    </div>
                                    <span style="font-size: 10px;">1.5k</span>
                                </div>
                                <div style="text-align: center;">
                                    <div style="width: 45px; height: 45px; background: rgba(255,255,255,0.2); border-radius: 50%; display: flex; align-items: center; justify-content: center; margin-bottom: 5px;">
                                        <i class="fas fa-comment" style="font-size: 18px;"></i>
                                    </div>
                                    <span style="font-size: 10px;">568</span>
                                </div>
                                <div style="text-align: center;">
                                    <div style="width: 45px; height: 45px; background: rgba(255,255,255,0.2); border-radius: 50%; display: flex; align-items: center; justify-content: center; margin-bottom: 5px;">
                                        <i class="fas fa-gift" style="font-size: 18px;"></i>
                                    </div>
                                    <span style="font-size: 10px;">礼物</span>
                                </div>
                            </div>
                        </div>

                        <!-- 聊天消息区域 -->
                        <div style="flex: 1; background: rgba(0,0,0,0.1); padding: 15px; overflow-y: auto;">
                            <div style="margin-bottom: 10px;">
                                <div style="background: rgba(255,255,255,0.9); padding: 8px 12px; border-radius: 15px; display: inline-block; max-width: 80%; font-size: 12px;">
                                    <span style="color: #ff6b9d; font-weight: bold;">小可爱123：</span>
                                    <span style="color: #333;">主播唱得真好听！</span>
                                </div>
                            </div>
                            <div style="margin-bottom: 10px;">
                                <div style="background: rgba(255,255,255,0.9); padding: 8px 12px; border-radius: 15px; display: inline-block; max-width: 80%; font-size: 12px;">
                                    <span style="color: #667eea; font-weight: bold;">音乐爱好者：</span>
                                    <span style="color: #333;">能唱一首《月亮代表我的心》吗？</span>
                                </div>
                            </div>
                            <div style="margin-bottom: 10px;">
                                <div style="background: rgba(255,215,0,0.9); padding: 8px 12px; border-radius: 15px; display: inline-block; max-width: 80%; font-size: 12px;">
                                    <span style="color: #ff4757; font-weight: bold;">🎁 土豪大佬</span>
                                    <span style="color: #333;"> 送出了 玫瑰花 x5</span>
                                </div>
                            </div>
                            <div style="margin-bottom: 10px;">
                                <div style="background: rgba(255,255,255,0.9); padding: 8px 12px; border-radius: 15px; display: inline-block; max-width: 80%; font-size: 12px;">
                                    <span style="color: #00b894; font-weight: bold;">夜猫子：</span>
                                    <span style="color: #333;">主播声音太治愈了</span>
                                </div>
                            </div>
                        </div>

                        <!-- 底部互动区域 -->
                        <div style="height: 60px; background: rgba(255,255,255,0.95); display: flex; align-items: center; padding: 0 15px; gap: 10px;">
                            <div style="flex: 1; background: #f5f5f5; border-radius: 20px; padding: 10px 15px; font-size: 14px; color: #999;">
                                说点什么...
                            </div>
                            <div style="width: 40px; height: 40px; background: linear-gradient(135deg, #ff6b9d, #c44569); border-radius: 50%; display: flex; align-items: center; justify-content: center;">
                                <i class="fas fa-gift" style="color: white; font-size: 16px;"></i>
                            </div>
                            <div style="width: 40px; height: 40px; background: linear-gradient(135deg, #ffd700, #ffb347); border-radius: 50%; display: flex; align-items: center; justify-content: center;">
                                <i class="fas fa-microphone" style="color: white; font-size: 16px;"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 聊天列表 -->
            <div>
                <div class="screen-title">聊天列表</div>
                <div class="phone-frame">
                    <div class="screen">
                        <div class="status-bar">
                            <span>9:41</span>
                            <span><i class="fas fa-signal"></i> <i class="fas fa-wifi"></i> <i class="fas fa-battery-three-quarters"></i></span>
                        </div>
                        <div class="header">
                            <h1>消息</h1>
                            <i class="fas fa-search"></i>
                        </div>
                        <div class="content">
                            <div class="chat-item">
                                <div class="avatar">小</div>
                                <div class="chat-info">
                                    <h3>坚强小猫咪</h3>
                                    <div class="last-message">你好，很高兴认识你</div>
                                </div>
                                <div class="chat-time">
                                    <div>14:30</div>
                                    <div class="badge" style="margin-top: 5px;">2</div>
                                </div>
                            </div>
                            <div class="chat-item">
                                <div class="avatar">美</div>
                                <div class="chat-info">
                                    <h3>美丽女孩</h3>
                                    <div class="last-message">今天天气真不错呢</div>
                                </div>
                                <div class="chat-time">昨天</div>
                            </div>
                            <div class="chat-item">
                                <div class="avatar">阳</div>
                                <div class="chat-info">
                                    <h3>阳光男孩</h3>
                                    <div class="last-message">一起去看电影吧</div>
                                </div>
                                <div class="chat-time">周二</div>
                            </div>
                        </div>
                        <div class="bottom-nav">
                            <div class="nav-item">
                                <i class="fas fa-home"></i>
                                <span>首页</span>
                            </div>
                            <div class="nav-item">
                                <i class="fas fa-compass"></i>
                                <span>直播</span>
                            </div>
                            <div class="nav-item active">
                                <i class="fas fa-comments"></i>
                                <span>消息</span>
                            </div>
                            <div class="nav-item">
                                <i class="fas fa-heart"></i>
                                <span>小宇宙</span>
                            </div>
                            <div class="nav-item">
                                <i class="fas fa-user"></i>
                                <span>我的</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 个人中心 -->
            <div>
                <div class="screen-title">个人中心</div>
                <div class="phone-frame">
                    <div class="screen">
                        <div class="status-bar">
                            <span>9:41</span>
                            <span><i class="fas fa-signal"></i> <i class="fas fa-wifi"></i> <i class="fas fa-battery-three-quarters"></i></span>
                        </div>
                        <div class="profile-header">
                            <div class="profile-avatar">
                                <i class="fas fa-user"></i>
                            </div>
                            <div class="profile-name">我的昵称</div>
                            <div class="profile-bio">这个人很懒，什么都没有留下</div>
                        </div>
                        <div class="content" style="height: calc(100% - 204px);">
                            <div class="menu-item">
                                <div class="menu-item-left">
                                    <i class="fas fa-edit"></i>
                                    <span>编辑资料</span>
                                </div>
                                <i class="fas fa-chevron-right" style="color: #ccc;"></i>
                            </div>
                            <div class="menu-item">
                                <div class="menu-item-left">
                                    <i class="fas fa-images"></i>
                                    <span>我的相册</span>
                                </div>
                                <i class="fas fa-chevron-right" style="color: #ccc;"></i>
                            </div>
                            <div class="menu-item">
                                <div class="menu-item-left">
                                    <i class="fas fa-heart"></i>
                                    <span>我的喜欢</span>
                                </div>
                                <i class="fas fa-chevron-right" style="color: #ccc;"></i>
                            </div>
                            <div class="menu-item">
                                <div class="menu-item-left">
                                    <i class="fas fa-eye"></i>
                                    <span>访客记录</span>
                                </div>
                                <i class="fas fa-chevron-right" style="color: #ccc;"></i>
                            </div>
                            <div class="menu-item">
                                <div class="menu-item-left">
                                    <i class="fas fa-cog"></i>
                                    <span>设置</span>
                                </div>
                                <i class="fas fa-chevron-right" style="color: #ccc;"></i>
                            </div>
                        </div>
                        <div class="bottom-nav">
                            <div class="nav-item">
                                <i class="fas fa-home"></i>
                                <span>首页</span>
                            </div>
                            <div class="nav-item">
                                <i class="fas fa-compass"></i>
                                <span>直播</span>
                            </div>
                            <div class="nav-item">
                                <i class="fas fa-comments"></i>
                                <span>消息</span>
                            </div>
                            <div class="nav-item">
                                <i class="fas fa-heart"></i>
                                <span>小宇宙</span>
                            </div>
                            <div class="nav-item active">
                                <i class="fas fa-user"></i>
                                <span>我的</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 聊天界面 -->
            <div>
                <div class="screen-title">聊天界面</div>
                <div class="phone-frame">
                    <div class="screen">
                        <div class="status-bar">
                            <span>9:41</span>
                            <span><i class="fas fa-signal"></i> <i class="fas fa-wifi"></i> <i class="fas fa-battery-three-quarters"></i></span>
                        </div>
                        <div class="header">
                            <div style="display: flex; align-items: center;">
                                <i class="fas fa-arrow-left" style="margin-right: 15px;"></i>
                                <div class="avatar" style="width: 35px; height: 35px; margin-right: 10px;">小</div>
                                <h1>坚强小猫咪</h1>
                            </div>
                            <i class="fas fa-ellipsis-v"></i>
                        </div>
                        <div class="content" style="padding: 15px; background: #f8f9fa;">
                            <div style="display: flex; margin-bottom: 15px;">
                                <div class="avatar" style="width: 35px; height: 35px; margin-right: 10px;">小</div>
                                <div style="background: white; padding: 10px 15px; border-radius: 18px; max-width: 70%; box-shadow: 0 1px 2px rgba(0,0,0,0.1);">
                                    你好，很高兴认识你！
                                </div>
                            </div>
                            <div style="display: flex; justify-content: flex-end; margin-bottom: 15px;">
                                <div style="background: linear-gradient(135deg, #ff6b9d, #c44569); color: white; padding: 10px 15px; border-radius: 18px; max-width: 70%;">
                                    你好！我也很高兴认识你
                                </div>
                            </div>
                            <div style="display: flex; margin-bottom: 15px;">
                                <div class="avatar" style="width: 35px; height: 35px; margin-right: 10px;">小</div>
                                <div style="background: white; padding: 10px 15px; border-radius: 18px; max-width: 70%; box-shadow: 0 1px 2px rgba(0,0,0,0.1);">
                                    你平时都喜欢做什么呢？
                                </div>
                            </div>
                        </div>
                        <div style="height: 60px; background: white; border-top: 1px solid #eee; display: flex; align-items: center; padding: 0 15px;">
                            <i class="fas fa-plus-circle" style="color: #999; margin-right: 10px; font-size: 24px;"></i>
                            <div style="flex: 1; background: #f5f5f5; border-radius: 20px; padding: 10px 15px; margin-right: 10px;">
                                输入消息...
                            </div>
                            <i class="fas fa-microphone" style="color: #ff6b9d; font-size: 24px;"></i>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 发布动态 -->
            <div>
                <div class="screen-title">发布动态</div>
                <div class="phone-frame">
                    <div class="screen">
                        <div class="status-bar">
                            <span>9:41</span>
                            <span><i class="fas fa-signal"></i> <i class="fas fa-wifi"></i> <i class="fas fa-battery-three-quarters"></i></span>
                        </div>
                        <div class="header">
                            <div style="display: flex; align-items: center;">
                                <i class="fas fa-times" style="margin-right: 15px;"></i>
                                <h1>发布动态</h1>
                            </div>
                            <button style="background: linear-gradient(135deg, #ff6b9d, #c44569); color: white; border: none; padding: 8px 16px; border-radius: 15px; font-size: 14px;">发布</button>
                        </div>
                        <div class="content" style="padding: 20px;">
                            <div style="display: flex; margin-bottom: 20px;">
                                <div class="avatar" style="width: 40px; height: 40px; margin-right: 15px;">我</div>
                                <div style="flex: 1;">
                                    <div style="background: #f8f9fa; border-radius: 10px; padding: 15px; min-height: 120px; border: 1px solid #eee;">
                                        <div style="color: #999;">分享你的生活...</div>
                                    </div>
                                </div>
                            </div>
                            <div style="display: grid; grid-template-columns: repeat(3, 1fr); gap: 10px; margin-bottom: 20px;">
                                <div style="aspect-ratio: 1; background: #f0f0f0; border-radius: 8px; display: flex; align-items: center; justify-content: center; border: 2px dashed #ddd;">
                                    <i class="fas fa-plus" style="color: #999; font-size: 24px;"></i>
                                </div>
                            </div>
                            <div style="display: flex; justify-content: space-around; padding: 20px 0; border-top: 1px solid #eee;">
                                <div style="display: flex; flex-direction: column; align-items: center; color: #666;">
                                    <i class="fas fa-camera" style="font-size: 24px; margin-bottom: 5px; color: #ff6b9d;"></i>
                                    <span style="font-size: 12px;">拍照</span>
                                </div>
                                <div style="display: flex; flex-direction: column; align-items: center; color: #666;">
                                    <i class="fas fa-images" style="font-size: 24px; margin-bottom: 5px; color: #ff6b9d;"></i>
                                    <span style="font-size: 12px;">相册</span>
                                </div>
                                <div style="display: flex; flex-direction: column; align-items: center; color: #666;">
                                    <i class="fas fa-map-marker-alt" style="font-size: 24px; margin-bottom: 5px; color: #ff6b9d;"></i>
                                    <span style="font-size: 12px;">位置</span>
                                </div>
                                <div style="display: flex; flex-direction: column; align-items: center; color: #666;">
                                    <i class="fas fa-smile" style="font-size: 24px; margin-bottom: 5px; color: #ff6b9d;"></i>
                                    <span style="font-size: 12px;">表情</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 个人资料页 -->
            <div>
                <div class="screen-title">个人资料页</div>
                <div class="phone-frame">
                    <div class="screen">
                        <div class="status-bar">
                            <span>9:41</span>
                            <span><i class="fas fa-signal"></i> <i class="fas fa-wifi"></i> <i class="fas fa-battery-three-quarters"></i></span>
                        </div>
                        <div class="header">
                            <div style="display: flex; align-items: center;">
                                <i class="fas fa-arrow-left" style="margin-right: 15px;"></i>
                                <h1>坚强小猫咪</h1>
                            </div>
                            <i class="fas fa-ellipsis-v"></i>
                        </div>
                        <div class="content">
                            <div style="background: linear-gradient(135deg, #ff6b9d, #c44569); padding: 30px 20px; text-align: center; color: white;">
                                <div style="width: 80px; height: 80px; border-radius: 50%; background: rgba(255,255,255,0.2); margin: 0 auto 15px; display: flex; align-items: center; justify-content: center; font-size: 24px;">
                                    小
                                </div>
                                <div style="font-size: 20px; margin-bottom: 5px;">坚强小猫咪</div>
                                <div style="font-size: 14px; opacity: 0.9;">21岁 · 146.73km</div>
                                <div style="display: flex; justify-content: center; gap: 20px; margin-top: 20px;">
                                    <button style="background: rgba(255,255,255,0.2); border: 1px solid rgba(255,255,255,0.3); color: white; padding: 8px 20px; border-radius: 20px; font-size: 14px;">
                                        <i class="fas fa-heart" style="margin-right: 5px;"></i>喜欢
                                    </button>
                                    <button style="background: rgba(255,255,255,0.2); border: 1px solid rgba(255,255,255,0.3); color: white; padding: 8px 20px; border-radius: 20px; font-size: 14px;">
                                        <i class="fas fa-comment" style="margin-right: 5px;"></i>聊天
                                    </button>
                                </div>
                            </div>
                            <div style="padding: 20px;">
                                <div style="margin-bottom: 20px;">
                                    <h3 style="margin-bottom: 10px; color: #333;">个人简介</h3>
                                    <p style="color: #666; line-height: 1.5;">新人上路，希望附近的小伙伴多多关照。喜欢旅行、摄影和美食，期待遇到有趣的灵魂。</p>
                                </div>
                                <div style="margin-bottom: 20px;">
                                    <h3 style="margin-bottom: 10px; color: #333;">兴趣爱好</h3>
                                    <div style="display: flex; flex-wrap: wrap; gap: 8px;">
                                        <span style="background: #f0f0f0; padding: 5px 12px; border-radius: 15px; font-size: 12px; color: #666;">旅行</span>
                                        <span style="background: #f0f0f0; padding: 5px 12px; border-radius: 15px; font-size: 12px; color: #666;">摄影</span>
                                        <span style="background: #f0f0f0; padding: 5px 12px; border-radius: 15px; font-size: 12px; color: #666;">美食</span>
                                        <span style="background: #f0f0f0; padding: 5px 12px; border-radius: 15px; font-size: 12px; color: #666;">电影</span>
                                    </div>
                                </div>
                                <div>
                                    <h3 style="margin-bottom: 10px; color: #333;">相册</h3>
                                    <div style="display: grid; grid-template-columns: repeat(3, 1fr); gap: 5px;">
                                        <div style="aspect-ratio: 1; background: linear-gradient(45deg, #ffeaa7, #fab1a0); border-radius: 8px; display: flex; align-items: center; justify-content: center; color: white; font-size: 12px;">📸</div>
                                        <div style="aspect-ratio: 1; background: linear-gradient(45deg, #a29bfe, #6c5ce7); border-radius: 8px; display: flex; align-items: center; justify-content: center; color: white; font-size: 12px;">📸</div>
                                        <div style="aspect-ratio: 1; background: linear-gradient(45deg, #fd79a8, #e84393); border-radius: 8px; display: flex; align-items: center; justify-content: center; color: white; font-size: 12px;">📸</div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</body>
</html>
